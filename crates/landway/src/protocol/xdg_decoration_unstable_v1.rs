// MIT/Apache2 License
// This file is automatically generated! Do not edit this file manually.
// Please see the landway-gen code for more info.

//! The `xdg_decoration_unstable_v1` protocol.

#![allow(
    clippy::doc_lazy_continuation,
    clippy::doc_markdown,
    clippy::match_single_binding,
    clippy::missing_errors_doc,
    clippy::missing_panics_doc,
    clippy::unreadable_literal,
    clippy::redundant_closure_for_method_calls,
    clippy::too_many_lines,
    unused_imports,
    unused_mut
)]

#[allow(clippy::wildcard_imports)]
use super::__all::*;

// Original Wayland copyright information
// -------------------------------------------------
// 
// Copyright © 2018 Simon Ser
// 
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the "Software"),
// to deal in the Software without restriction, including without limitation
// the rights to use, copy, modify, merge, publish, distribute, sublicense,
// and/or sell copies of the Software, and to permit persons to whom the
// Software is furnished to do so, subject to the following conditions:
// 
// The above copyright notice and this permission notice (including the next
// paragraph) shall be included in all copies or substantial portions of the
// Software.
// 
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
// DEALINGS IN THE SOFTWARE.
// 
// -------------------------------------------------

pub mod zxdg_decoration_manager_v1 {
    //!  zxdg_decoration_manager_v1: window decoration manager
    //! 
    //!  
    //!  This interface allows a compositor to announce support for server-side
    //!  decorations.
    //!  
    //!  A window decoration is a set of window controls as deemed appropriate by
    //!  the party managing them, such as user interface components used to move,
    //!  resize and change a window's state.
    //!  
    //!  A client can use this protocol to request being decorated by a supporting
    //!  compositor.
    //!  
    //!  If compositor and client do not negotiate the use of a server-side
    //!  decoration using this protocol, clients continue to self-decorate as they
    //!  see fit.
    //!  
    //!  Warning! The protocol described in this file is experimental and
    //!  backward incompatible changes may be made. Backward compatible changes
    //!  may be added together with the corresponding interface version bump.
    //!  Backward incompatible changes are done by bumping the version number in
    //!  the protocol and interface names and resetting the interface version.
    //!  Once the protocol is to be declared stable, the 'z' prefix and the
    //!  version number in the protocol and interface names are removed and the
    //!  interface version number is reset.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `zxdg_decoration_manager_v1` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct ZxdgDecorationManagerV1(Proxy);

    impl fmt::Debug for ZxdgDecorationManagerV1 {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("ZxdgDecorationManagerV1").field(&self.0).finish()
        }
    }

    impl From<Proxy> for ZxdgDecorationManagerV1 {
        fn from(proxy: Proxy) -> Self {
            ZxdgDecorationManagerV1(proxy)
        }
    }

    impl From<ZxdgDecorationManagerV1> for Proxy {
        fn from(proxy: ZxdgDecorationManagerV1) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for ZxdgDecorationManagerV1 {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for ZxdgDecorationManagerV1 {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl ZxdgDecorationManagerV1 {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl ZxdgDecorationManagerV1 {
        pub fn destroy(
            &self,
        ) -> io::Result<()> {
            //!  destroy: destroy the decoration manager object
            //! 
            //!  
            //!  Destroy the decoration manager. This doesn't destroy objects created
            //!  with the manager.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn get_toplevel_decoration(
            &self,
            event_queue: &crate::EventQueue,
            toplevel: &super::xdg_toplevel::XdgToplevel, 
        ) -> io::Result<super::zxdg_toplevel_decoration_v1::ZxdgToplevelDecorationV1> {
            //!  get_toplevel_decoration: create a new toplevel decoration object
            //! 
            //!  
            //!  Create a new decoration object associated with the given toplevel.
            //!  
            //!  Creating an xdg_toplevel_decoration from an xdg_toplevel which has a
            //!  buffer attached or committed is a client error, and any attempts by a
            //!  client to attach or manipulate a buffer prior to the first
            //!  xdg_toplevel_decoration.configure event must also be treated as
            //!  errors.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (NewId, 0),
                (Object, Some(toplevel.as_ref())),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::zxdg_toplevel_decoration_v1::ZxdgToplevelDecorationV1::INTERFACE,
                super::zxdg_toplevel_decoration_v1::ZxdgToplevelDecorationV1::VERSION,
                event_queue
            )?;
            Ok(super::zxdg_toplevel_decoration_v1::ZxdgToplevelDecorationV1::from(proxy))
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 1;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("zxdg_decoration_manager_v1"),
        1,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("destroy"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("get_toplevel_decoration"),
            sig!("no"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::zxdg_toplevel_decoration_v1::INTERFACE), Some(&super::xdg_toplevel::INTERFACE), ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
    ];

}

pub use self::zxdg_decoration_manager_v1::ZxdgDecorationManagerV1;

pub mod zxdg_toplevel_decoration_v1 {
    //!  zxdg_toplevel_decoration_v1: decoration object for a toplevel surface
    //! 
    //!  
    //!  The decoration object allows the compositor to toggle server-side window
    //!  decorations for a toplevel surface. The client can request to switch to
    //!  another mode.
    //!  
    //!  The xdg_toplevel_decoration object must be destroyed before its
    //!  xdg_toplevel.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `zxdg_toplevel_decoration_v1` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct ZxdgToplevelDecorationV1(Proxy);

    impl fmt::Debug for ZxdgToplevelDecorationV1 {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("ZxdgToplevelDecorationV1").field(&self.0).finish()
        }
    }

    impl From<Proxy> for ZxdgToplevelDecorationV1 {
        fn from(proxy: Proxy) -> Self {
            ZxdgToplevelDecorationV1(proxy)
        }
    }

    impl From<ZxdgToplevelDecorationV1> for Proxy {
        fn from(proxy: ZxdgToplevelDecorationV1) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for ZxdgToplevelDecorationV1 {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for ZxdgToplevelDecorationV1 {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl ZxdgToplevelDecorationV1 {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl ZxdgToplevelDecorationV1 {
        pub fn destroy(
            &self,
        ) -> io::Result<()> {
            //!  destroy: destroy the decoration object
            //! 
            //!  
            //!  Switch back to a mode without any server-side decorations at the next
            //!  commit.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_mode(
            &self,
            mode: Mode,
        ) -> io::Result<()> {
            //!  set_mode: set the decoration mode
            //! 
            //!  
            //!  Set the toplevel surface decoration mode. This informs the compositor
            //!  that the client prefers the provided decoration mode.
            //!  
            //!  After requesting a decoration mode, the compositor will respond by
            //!  emitting an xdg_surface.configure event. The client should then update
            //!  its content, drawing it without decorations if the received mode is
            //!  server-side decorations. The client must also acknowledge the configure
            //!  when committing the new content (see xdg_surface.ack_configure).
            //!  
            //!  The compositor can decide not to use the client's mode and enforce a
            //!  different mode instead.
            //!  
            //!  Clients whose decoration mode depend on the xdg_toplevel state may send
            //!  a set_mode request in response to an xdg_surface.configure event and wait
            //!  for the next xdg_surface.configure event to prevent unwanted state.
            //!  Such clients are responsible for preventing configure loops and must
            //!  make sure not to send multiple successive set_mode requests with the
            //!  same decoration mode.
            //!  
            //!  If an invalid mode is supplied by the client, the invalid_mode protocol
            //!  error is raised by the compositor.
            //!  

            const OPCODE: u32 = 1;

            let mode = mode.into();
            crate::args!(args = 
                (UInt32, mode),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn unset_mode(
            &self,
        ) -> io::Result<()> {
            //!  unset_mode: unset the decoration mode
            //! 
            //!  
            //!  Unset the toplevel surface decoration mode. This informs the compositor
            //!  that the client doesn't prefer a particular decoration mode.
            //!  
            //!  This request has the same semantics as set_mode.
            //!  

            const OPCODE: u32 = 2;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let mode = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let mode = Mode::from(mode);

                        assert!(iter.next().is_none());
                        let event = Event::Configure {
                            mode,
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 1;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  configure: notify a decoration mode change
        /// 
        ///  
        ///  The configure event configures the effective decoration mode. The
        ///  configured state should not be applied immediately. Clients must send an
        ///  ack_configure in response to this event. See xdg_surface.configure and
        ///  xdg_surface.ack_configure for details.
        ///  
        ///  A configure event can be sent at any time. The specified mode must be
        ///  obeyed by the client.
        ///  
        Configure {
            /// mode - the decoration mode
            mode: Mode,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("zxdg_toplevel_decoration_v1"),
        1,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("destroy"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_mode"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("unset_mode"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("configure"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// unconfigured_buffer - xdg_toplevel has a buffer attached before configure
        pub const UNCONFIGURED_BUFFER: Error = Error(0);

        /// already_constructed - xdg_toplevel already has a decoration object
        pub const ALREADY_CONSTRUCTED: Error = Error(1);

        /// orphaned - xdg_toplevel destroyed before the decoration object
        pub const ORPHANED: Error = Error(2);

        /// invalid_mode - invalid mode
        pub const INVALID_MODE: Error = Error(3);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

    ///  mode: window decoration modes
    /// 
    ///  
    ///  These values describe window decoration modes.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Mode(u32);

    impl Mode {
        /// client_side - no server-side window decoration
        pub const CLIENT_SIDE: Mode = Mode(1);

        /// server_side - server-side window decoration
        pub const SERVER_SIDE: Mode = Mode(2);

    }

    impl From<u32> for Mode {
        fn from(value: u32) -> Self {
            Mode(value)
        }
    }

    impl From<i32> for Mode {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Mode::from(value as u32)
        }
    }

    impl From<Mode> for u32 {
        fn from(value: Mode) -> Self {
            value.0
        }
    }

    impl From<Mode> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Mode) -> Self {
            value.0 as i32
        }
    }

}

pub use self::zxdg_toplevel_decoration_v1::ZxdgToplevelDecorationV1;

