// MIT/Apache2 License

use std::io;

use winit_lite::{Display, Event, Poller, WakeupCause, WindowAttributes};

fn main() -> io::Result<()> {
    let mut poller = Poller::new()?;
    let mut window = None;

    poller.run(move |display: &mut Display<'_>, event| match event {
        Event::Wakeup(cause) => {
            if let WakeupCause::Init = cause {
                window = Some(display.create_window(WindowAttributes::default()).unwrap());
                display.set_timeout(std::time::Duration::from_secs(2));
            } else if let WakeupCause::WaitTimedOut = cause {
                display.exit();
            }
        }
        _ => {}
    })
}
