// MIT/Apache2 License

#![forbid(unsafe_code)]

//! Copy raw image information onto a window.

use std::borrow::{Borrow, BorrowMut};
use std::io;
use std::marker::PhantomData;
use std::mem::MaybeUninit;
use std::num::NonZeroU32;
use std::ops::{Deref, DerefMut};
use std::rc::Rc;

use raw_window_handle::{
    DisplayHandle, HandleError, HasDisplayHandle, HasWindowHandle, WindowHandle,
};

//#[cfg(windows)]
//mod windows;
//#[cfg(windows)]
//use windows as sys;

#[cfg(not(any(windows, target_os = "macos")))]
mod wayland;
#[cfg(not(any(windows, target_os = "macos")))]
use wayland as sys;

/// Context for creating surfaces.
pub struct Context<P> {
    /// Inner context.
    inner: Rc<sys::Context<P>>,
}

impl<P: HasDisplayHandle> Context<P> {
    /// Create a new context based around a display handle.
    pub fn new(provider: P) -> io::Result<Self> {
        sys::Context::new(provider).map(|inner| Self {
            inner: Rc::new(inner),
        })
    }

    /// Get a reference to the provider.
    #[inline]
    #[must_use]
    pub fn as_provider(&self) -> &P {
        self.inner.as_provider()
    }

    /// Create a new surface.
    #[inline]
    pub fn create_surface<W: HasWindowHandle>(
        &self,
        provider: W,
        width: NonZeroU32,
        height: NonZeroU32,
    ) -> io::Result<Surface<P, W>> {
        sys::Surface::new(provider, (width, height), &self.inner).map(|inner| Surface {
            context: Rc::clone(&self.inner),
            inner,
        })
    }
}

impl<P: HasDisplayHandle> HasDisplayHandle for Context<P> {
    #[inline]
    fn display_handle(&self) -> Result<DisplayHandle<'_>, HandleError> {
        self.as_provider().display_handle()
    }
}

/// A surface to draw into.
pub struct Surface<D, P> {
    /// Reference to the context.
    context: Rc<sys::Context<D>>,

    /// Inner surface.
    inner: sys::Surface<P>,
}

impl<D, P: HasWindowHandle> Surface<D, P> {
    /// Get a reference to the provider.
    #[inline]
    #[must_use]
    pub fn as_provider(&self) -> &P {
        self.inner.as_provider()
    }

    /// Get the current width of the surface.
    #[inline]
    #[must_use]
    pub fn width(&self) -> NonZeroU32 {
        self.inner.size().0
    }

    /// Get the current height of the surface.
    #[inline]
    #[must_use]
    pub fn height(&self) -> NonZeroU32 {
        self.inner.size().1
    }

    /// Resize the surface.
    #[inline]
    pub fn resize(&mut self, width: NonZeroU32, height: NonZeroU32) -> io::Result<()> {
        self.inner.resize(&self.context, (width, height))
    }

    /// Acquire a buffer to write into.
    #[inline]
    pub fn acquire(&mut self) -> io::Result<Buffer<'_, D, P>> {
        self.inner
            .acquire(&self.context)
            .map(|_| Buffer { surface: self })
    }
}

impl<D, P: HasWindowHandle> HasWindowHandle for Surface<D, P> {
    #[inline]
    fn window_handle(&self) -> Result<WindowHandle<'_>, HandleError> {
        self.as_provider().window_handle()
    }
}

/// The buffer to write pixels into.
pub struct Buffer<'a, D, P> {
    /// Reference to the surface.
    surface: &'a mut Surface<D, P>,
}

impl<'a, D, P: HasWindowHandle> AsRef<[MaybeUninit<u32>]> for Buffer<'a, D, P> {
    #[inline]
    fn as_ref(&self) -> &[MaybeUninit<u32>] {
        self.surface.inner.buffer()
    }
}

impl<'a, D, P: HasWindowHandle> AsMut<[MaybeUninit<u32>]> for Buffer<'a, D, P> {
    #[inline]
    fn as_mut(&mut self) -> &mut [MaybeUninit<u32>] {
        self.surface.inner.buffer_mut()
    }
}

impl<'a, D, P: HasWindowHandle> Borrow<[MaybeUninit<u32>]> for Buffer<'a, D, P> {
    #[inline]
    fn borrow(&self) -> &[MaybeUninit<u32>] {
        self.as_ref()
    }
}

impl<'a, D, P: HasWindowHandle> BorrowMut<[MaybeUninit<u32>]> for Buffer<'a, D, P> {
    #[inline]
    fn borrow_mut(&mut self) -> &mut [MaybeUninit<u32>] {
        self.as_mut()
    }
}

impl<'a, D, P: HasWindowHandle> Deref for Buffer<'a, D, P> {
    type Target = [MaybeUninit<u32>];

    #[inline]
    fn deref(&self) -> &Self::Target {
        self.as_ref()
    }
}

impl<'a, D, P: HasWindowHandle> DerefMut for Buffer<'a, D, P> {
    #[inline]
    fn deref_mut(&mut self) -> &mut Self::Target {
        self.as_mut()
    }
}

impl<'a, D, P: HasWindowHandle> Buffer<'a, D, P> {
    /// Get the age of the buffer.
    #[inline]
    #[must_use]
    pub fn age(&self) -> u8 {
        self.surface.inner.age()
    }

    /// Present the buffer to the screen.
    #[inline]
    pub fn present_with_damage(self, damage: &[Rectangle]) -> io::Result<()> {
        self.surface.inner.present(&self.surface.context, damage)
    }

    /// Present the buffer to the screen, damaging the entire surface.
    #[inline]
    pub fn present(self) -> io::Result<()> {
        let rect = Rectangle {
            x: 0,
            y: 0,
            width: self.surface.width(),
            height: self.surface.height(),
        };
        self.present_with_damage(&[rect])
    }
}

/// Rectangle of pixels on the screen.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub struct Rectangle {
    /// X coordinate.
    pub x: u32,

    /// Y coordinate.
    pub y: u32,

    /// Width.
    pub width: NonZeroU32,

    /// Height.
    pub height: NonZeroU32,
}
