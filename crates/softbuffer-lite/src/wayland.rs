// MIT/Apache2 License

use crate::Rectangle;
use landway::protocol::wayland as wl;
use landway::{Disp<PERSON>, DisplayProvider, EventQueue, <PERSON>hm<PERSON><PERSON><PERSON>, WindowProvider};
use raw_window_handle::{<PERSON><PERSON><PERSON>playHandle, Has<PERSON>indowHandle, RawDisplayHandle};
use rustix::event;

use std::cell::RefCell;
use std::io;
use std::mem::MaybeUninit;
use std::num::NonZeroU32;
use std::rc::Rc;

/// Context implementation.
pub(crate) struct Context<P> {
    /// Provider for the display.
    display: DisplayProvider<P>,

    /// Event queue.
    queue: RefCell<EventQueue>,

    /// SHM interface.
    shm: wl::WlShm,
}

impl<P: HasDisplayHandle> Context<P> {
    /// Create a new context object.
    pub(crate) fn new(provider: P) -> io::Result<Self> {
        /// State for Wayland initialization.
        struct State {
            /// Wait for the SHM interface to appear.
            shm: Option<(u32, u32)>,

            /// Wait for the synchronization to complete.
            synced: bool,
        }

        // Fetch display and queue.
        let display = DisplayProvider::new(provider)?;
        let mut queue = display.create_queue(None)?;

        // Get the registry.
        let mut display_proxy = display.display_proxy();
        let mut registry = display_proxy.get_registry(&queue)?;

        // Set up a listener.
        let state = Rc::new(RefCell::new(State {
            shm: None,
            synced: false,
        }));
        registry.add_listener({
            use wl::wl_registry::Event;

            let shm = state.clone();
            move |_, event| {
                if let Event::Global {
                    name,
                    interface,
                    version,
                } = event
                    && interface.to_str().ok() == Some("wl_shm")
                {
                    shm.borrow_mut().shm = Some((name, version));
                }
            }
        })?;

        // Perform a round-trip.
        let sync_cb = display_proxy.sync(&queue)?;
        sync_cb.add_listener({
            let state = state.clone();
            move |_, _| {
                state.borrow_mut().synced = true;
            }
        })?;

        // Wait for the round-trip to complete.
        while !state.borrow().synced {
            dispatch(&display, &mut queue)?;
        }

        // We should have received an SHM object.
        let shm = match state.borrow_mut().shm.take() {
            Some((name, version)) => registry
                .bind(name, wl::WlShm::INTERFACE, version, &queue)?
                .into(),

            None => {
                return Err(io::Error::new(
                    io::ErrorKind::NotFound,
                    "wl_shm interface not found",
                ));
            }
        };

        Ok(Self {
            display,
            queue: RefCell::new(queue),
            shm,
        })
    }

    /// Get the underlying provider.
    #[inline]
    #[must_use]
    pub(crate) fn as_provider(&self) -> &P {
        self.display.as_provider()
    }
}

/// Surface implementation for Wayland.
pub(crate) struct Surface<P> {
    /// Provider for the window.
    window: WindowProvider<P>,

    /// Current size of the window.
    size: (NonZeroU32, NonZeroU32),

    /// Front and back buffers.
    buffers: (Buffer, Buffer),

    /// Pending resize for the front buffer.
    pending_resize: bool,
}

impl<P: HasWindowHandle> Surface<P> {
    /// Create a new surface.
    pub(crate) fn new<D>(
        provider: P,
        size: (NonZeroU32, NonZeroU32),
        context: &Context<D>,
    ) -> io::Result<Self> {
        let window = WindowProvider::new(provider)?;
        let (width, height) = size;

        // Create front and back buffers using shared memory
        let front_buffer = Buffer::new(&context, width.get(), height.get())?;
        let back_buffer = Buffer::new(&context, width.get(), height.get())?;

        Ok(Self {
            window,
            size,
            buffers: (front_buffer, back_buffer),
            pending_resize: false,
        })
    }

    /// Get a reference to the provider.
    #[inline]
    #[must_use]
    pub(crate) fn as_provider(&self) -> &P {
        self.window.as_provider()
    }

    /// Get the size of the surface.
    #[inline]
    #[must_use]
    pub(crate) fn size(&self) -> (NonZeroU32, NonZeroU32) {
        self.size
    }

    /// Resize the buffers.
    pub(crate) fn resize<D>(
        &mut self,
        context: &Context<D>,
        dims: (NonZeroU32, NonZeroU32),
    ) -> io::Result<()> {
        // Wait for the back buffer to be released.
        let (front, back) = &mut self.buffers;
        while !back.inner.is_released() {
            dispatch(&context.display, &mut context.queue.borrow_mut())?;
        }

        // Resize the back buffer.
        back.inner
            .resize(&context.queue.borrow(), dims.0.get(), dims.1.get())?;

        // Queue a resize for the front buffer.
        if front.inner.is_released() {
            front
                .inner
                .resize(&context.queue.borrow(), dims.0.get(), dims.1.get())?;
        } else {
            self.pending_resize = true;
        }

        // Update the size.
        self.size = dims;

        Ok(())
    }

    /// Acquire the buffer.
    pub(crate) fn acquire<D>(&mut self, context: &Context<D>) -> io::Result<()> {
        let (_, back) = &mut self.buffers;

        // Wait for the buffer to be released.
        while !back.inner.is_released() {
            dispatch(&context.display, &mut context.queue.borrow_mut())?;
        }

        Ok(())
    }

    /// Get the buffer.
    pub(crate) fn buffer(&self) -> &[MaybeUninit<u32>] {
        self.buffers
            .1
            .inner
            .as_slice()
            .expect("buffer is not released")
    }

    /// Get the mutable buffer.
    pub(crate) fn buffer_mut(&mut self) -> &mut [MaybeUninit<u32>] {
        self.buffers
            .1
            .inner
            .as_mut_slice()
            .expect("buffer is not released")
    }

    /// Get the age of the buffer.
    pub(crate) fn age(&self) -> u8 {
        self.buffers.1.age
    }

    /// Present the buffer to the screen.
    pub(crate) fn present<D>(
        &mut self,
        context: &Context<D>,
        damage: &[Rectangle],
    ) -> io::Result<()> {
        let (front, back) = &mut self.buffers;

        // Swap the front and back buffers.
        std::mem::swap(front, back);

        // Set buffer ages.
        front.age = 1;
        if back.age != 0 {
            back.age += 1;
        }

        // Attach the front buffer to the surface.
        front.inner.attach(&mut self.window)?;

        if self.window.as_proxy().version() < 4 {
            // No partial damage option, damage the entire buffer.
            self.window.damage(0, 0, i32::MAX, i32::MAX)?;
        } else {
            for rect in damage {
                self.window.damage_buffer(
                    rect.x.try_into().map_err(|_| io::ErrorKind::InvalidInput)?,
                    rect.y.try_into().map_err(|_| io::ErrorKind::InvalidInput)?,
                    rect.width
                        .get()
                        .try_into()
                        .map_err(|_| io::ErrorKind::InvalidInput)?,
                    rect.height
                        .get()
                        .try_into()
                        .map_err(|_| io::ErrorKind::InvalidInput)?,
                )?;
            }
        }

        self.window.commit()?;
        context.display.flush()?;

        // If there is a pending resize, wait for the new back buffer to be released.
        if self.pending_resize {
            while !back.inner.is_released() {
                dispatch(&context.display, &mut context.queue.borrow_mut())?;
            }
            back.inner.resize(
                &context.queue.borrow(),
                self.size.0.get(),
                self.size.1.get(),
            )?;
            self.pending_resize = false;
        }

        Ok(())
    }
}

/// SHM buffer.
struct Buffer {
    /// Inner SHM buffer.
    inner: ShmBuffer,

    /// Age of the buffer.
    age: u8,
}

impl Buffer {
    /// Create a new buffer.
    fn new<P>(context: &Context<P>, width: u32, height: u32) -> io::Result<Self> {
        use rustix::shm;
        use std::ffi::CString;
        use std::{process, time};

        // Create a unique name for the shared memory object.
        let name = CString::new(format!(
            "softbuffer-{}-{}",
            process::id(),
            time::SystemTime::now()
                .duration_since(time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos()
        ))
        .map_err(|_| io::Error::new(io::ErrorKind::InvalidInput, "invalid shm name"))?;

        // Open shared memory with read/write access.
        let fd = shm::open(
            &name,
            shm::OFlags::RDWR | shm::OFlags::CREATE | shm::OFlags::EXCL,
            rustix::fs::Mode::RUSR | rustix::fs::Mode::WUSR,
        )?;

        // Use this to create the buffer.
        let buffer = ShmBuffer::new(fd, &context.shm, &context.queue.borrow(), width, height)?;

        Ok(Self {
            inner: buffer,
            age: 0,
        })
    }
}

/// Dispatch Wayland events and wait for more if needed.
fn dispatch(display: &Display, queue: &mut EventQueue) -> io::Result<()> {
    // Perform everything needed to synchronize.
    display.flush()?;

    // Dispatch the queue early.
    if display.dispatch_queue(queue)? > 0 {
        return Ok(());
    }

    // Begin a read.
    let read = display.read(queue)?;

    // Wait for new data.
    let mut fds = [event::PollFd::new(
        &read,
        event::PollFlags::IN | event::PollFlags::ERR,
    )];
    loop {
        match event::poll(&mut fds, None) {
            Ok(_) => break,
            Err(err) if err == rustix::io::Errno::INTR => continue,
            Err(err) => return Err(err.into()),
        }
    }

    // Read the events.
    read.read()?;

    Ok(())
}
