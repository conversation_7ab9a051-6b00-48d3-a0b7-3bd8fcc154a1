// MIT/Apache2 License

//! Wayland example for softbuffer-lite using landway.
//!
//! To run this example:
//! ```bash
//! cargo run --example wayland
//! ```
//!
//! Make sure you're running in a Wayland environment (e.g., GNOME on Wayland,
//! Sway, or other Wayland compositor).
//!
//! This example demonstrates how to:
//! 1. Connect to a Wayland display using landway
//! 2. Create a window with XDG shell protocol
//! 3. Use softbuffer-lite to create a drawing surface
//! 4. Draw a rainbow pattern by writing pixels directly to the buffer
//! 5. Present the buffer to the screen
//!
//! The example creates an 800x600 window and draws a horizontal rainbow gradient
//! that fades from bright at the top to darker at the bottom.

use landway::protocol::{wayland as wl, xdg_shell as xdgs};
use landway::{Display, EventQueue};
use softbuffer_lite::{Context, Rectangle};
use std::cell::RefCell;
use std::ffi::CStr;
use std::io;
use std::mem::MaybeUninit;
use std::num::NonZeroU32;
use std::rc::Rc;

/// Global Wayland object.
#[derive(Debug)]
struct Global {
    /// ID of the global.
    name: u32,
    /// Interface for the global.
    interface: String,
    /// Version of the global.
    version: u32,
}

/// Registry state.
struct Registry {
    /// Registry proxy.
    proxy: wl::WlRegistry,
    /// Globals found by the registry.
    globals: Vec<Global>,
}

impl Registry {
    /// Bind a global in the registry.
    fn bind<I>(
        &self,
        interface: &'static landway::Interface,
        version: u32,
        queue: &EventQueue,
    ) -> io::Result<I>
    where
        landway::Proxy: Into<I>,
    {
        // Find the global.
        let global = self
            .globals
            .iter()
            .find(|global| global.interface == interface.name().to_string_lossy());

        let (name, available_version) = match global {
            Some(global) => (global.name, global.version),
            None => {
                return Err(io::Error::new(
                    io::ErrorKind::NotFound,
                    format!("global {:?} not found", interface.name()),
                ));
            }
        };

        // Use the minimum of requested and available version
        let bind_version = version.min(available_version);

        // Perform the bind.
        let proxy = self.proxy.bind(name, interface, bind_version, queue)?;
        Ok(proxy.into())
    }
}

fn main() -> io::Result<()> {
    // Connect to the Wayland display
    let display = Display::connect()?;
    let mut queue = display.create_queue(None)?;

    // Get the registry to find global objects
    let display_proxy = display.display_proxy();
    let registry_proxy = display_proxy.get_registry(&queue)?;

    // Set up registry state
    let registry_state = Rc::new(RefCell::new(Registry {
        proxy: registry_proxy,
        globals: Vec::new(),
    }));

    // Set up registry listener to collect globals
    {
        let registry_state_clone = registry_state.clone();
        registry_state.borrow().proxy.add_listener(move |_, event| {
            let mut registry = registry_state_clone.borrow_mut();
            match event {
                wl::wl_registry::Event::Global { name, interface, version } => {
                    registry.globals.push(Global {
                        name,
                        interface: interface.to_string_lossy().into_owned(),
                        version,
                    });
                }
                wl::wl_registry::Event::GlobalRemove { name } => {
                    registry.globals.retain(|global| global.name != name);
                }
                _ => {}
            }
        })?;
    }

    // Perform a roundtrip to populate registry
    let cb = display_proxy.sync(&queue)?;
    let synced = Rc::new(RefCell::new(false));
    {
        let synced_clone = synced.clone();
        cb.add_listener(move |_, _| {
            *synced_clone.borrow_mut() = true;
        })?;
    }

    // Wait for synchronization to complete
    while !*synced.borrow() {
        display.flush()?;
        let read = display.read(&mut queue)?;
        read.read()?;
    }

    // Bind to the compositor and xdg_wm_base
    let compositor: wl::WlCompositor = registry_state.borrow().bind(
        wl::WlCompositor::INTERFACE,
        6,
        &queue,
    )?;
    let xdg_wm_base: xdgs::XdgWmBase = registry_state.borrow().bind(
        xdgs::XdgWmBase::INTERFACE,
        6,
        &queue,
    )?;

    // Create a surface
    let surface = compositor.create_surface(&queue)?;

    // Create XDG surface and toplevel for window management
    let xdg_surface = xdg_wm_base.get_xdg_surface(&queue, &surface)?;
    let xdg_toplevel = xdg_surface.get_toplevel(&queue)?;

    // Set window title
    xdg_toplevel.set_title(CStr::from_bytes_with_nul(b"Softbuffer-lite Wayland Rainbow Example\0").unwrap())?;

    // Configure the surface
    let width = 800u32;
    let height = 600u32;

    // Set up XDG surface listener for configure events
    let configured = Rc::new(RefCell::new(false));
    {
        let configured_clone = configured.clone();
        xdg_surface.add_listener(move |xdg_surface, event| {
            if let xdgs::xdg_surface::Event::Configure { serial } = event {
                // Acknowledge the configure event
                let _ = xdg_surface.ack_configure(serial);
                *configured_clone.borrow_mut() = true;
            }
        })?;
    }

    // Commit the surface to make it visible
    surface.commit()?;
    display.flush()?;

    // Wait for the configure event
    while !*configured.borrow() {
        let read = display.read(&mut queue)?;
        read.read()?;
    }

    // Create softbuffer-lite context and surface
    let context = Context::new(&display)?;
    let mut softbuffer_surface = context.create_surface(
        &surface,
        NonZeroU32::new(width).unwrap(),
        NonZeroU32::new(height).unwrap(),
    )?;

    // Draw a rainbow pattern
    {
        let mut buffer = softbuffer_surface.acquire()?;
        let buffer_slice = buffer.as_mut();

        // Generate rainbow pattern
        for y in 0..height {
            for x in 0..width {
                let index = (y * width + x) as usize;
                
                // Create a rainbow pattern based on position
                let hue = (x as f32 / width as f32) * 360.0;
                let saturation = 1.0;
                let value = 1.0 - (y as f32 / height as f32) * 0.5; // Fade from top to bottom
                
                let rgb = hsv_to_rgb(hue, saturation, value);
                let pixel = (0xFF << 24) | (rgb.0 << 16) | (rgb.1 << 8) | rgb.2;
                
                if index < buffer_slice.len() {
                    buffer_slice[index] = MaybeUninit::new(pixel);
                }
            }
        }

        // Present the buffer with full damage
        let damage = Rectangle {
            x: 0,
            y: 0,
            width: NonZeroU32::new(width).unwrap(),
            height: NonZeroU32::new(height).unwrap(),
        };
        buffer.present_with_damage(&[damage])?;
    }

    // Commit the surface to display the buffer
    surface.commit()?;
    display.flush()?;

    // Keep the window open for a few seconds
    std::thread::sleep(std::time::Duration::from_secs(5));

    println!("Rainbow pattern displayed successfully!");
    Ok(())
}

/// Convert HSV color to RGB
fn hsv_to_rgb(h: f32, s: f32, v: f32) -> (u32, u32, u32) {
    let c = v * s;
    let x = c * (1.0 - ((h / 60.0) % 2.0 - 1.0).abs());
    let m = v - c;

    let (r_prime, g_prime, b_prime) = if h < 60.0 {
        (c, x, 0.0)
    } else if h < 120.0 {
        (x, c, 0.0)
    } else if h < 180.0 {
        (0.0, c, x)
    } else if h < 240.0 {
        (0.0, x, c)
    } else if h < 300.0 {
        (x, 0.0, c)
    } else {
        (c, 0.0, x)
    };

    let r = ((r_prime + m) * 255.0) as u32;
    let g = ((g_prime + m) * 255.0) as u32;
    let b = ((b_prime + m) * 255.0) as u32;

    (r, g, b)
}
