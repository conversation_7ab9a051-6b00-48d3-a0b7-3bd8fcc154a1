[package]
name = "softbuffer-lite"
version = "0.1.0"
edition = "2024"

[dependencies]
raw-window-handle = { version = "0.6.2", default-features = false }

[target.'cfg(windows)'.dependencies]
porcupine = { path = "../porcupine" }

[target.'cfg(not(any(windows, target_os = "macos")))'.dependencies]
landway = { path = "../landway", features = ["raw-window-handle", "shm"] }

[target.'cfg(not(any(windows, target_os = "macos")))'.dependencies.rustix]
version = "1.0.7"
default-features = false
features = ["event", "fs", "pipe", "std", "shm"]

[target.'cfg(not(any(windows, target_os = "macos")))'.dev-dependencies]
landway = { path = "../landway", features = ["xdg_shell"] }
