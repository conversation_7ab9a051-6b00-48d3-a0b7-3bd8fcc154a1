// MIT/Apache2 License

//! Handle types.

use windows_sys::Win32::Foundation as found;
use windows_sys::Win32::UI::WindowsAndMessaging as winmsg;

macro_rules! define_handle {
    (
        $real:ty,
        $raw:ident,
        $as_raw:ident,
        $owned:ident,
        $borrowed:ident,
        $as_borrowed:ident,
        $drop_fn:expr
    ) => {
        #[doc = concat!("Raw ", stringify!($raw), " handle type.")]
        ///
        /// This is a type alias for the underlying Windows handle type.
        pub type $raw = $real;

        #[doc = concat!("Trait for extracting raw ", stringify!($raw), " handles.")]
        ///
        /// This trait provides a way to extract the raw handle value from
        /// handle wrapper types.
        pub trait $as_raw {
            #[doc = concat!("Extracts the raw ", stringify!($raw), " handle.")]
            fn as_raw(&self) -> $raw;
        }

        #[doc = concat!("An owned ", stringify!($raw), " handle.")]
        ///
        /// This type represents an owned handle that will be automatically
        /// closed when dropped. It provides safe ownership semantics for
        /// Windows handles.
        #[derive(Debug)]
        pub struct $owned {
            handle: $raw,
        }

        #[doc = concat!("A borrowed ", stringify!($raw), " handle.")]
        ///
        /// This type represents a borrowed reference to a handle. It does not
        /// own the handle and will not close it when dropped.
        #[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash)]
        pub struct $borrowed<'a> {
            handle: $raw,
            _phantom: std::marker::PhantomData<&'a $owned>,
        }

        #[doc = concat!("Trait for borrowing ", stringify!($raw), " handles.")]
        ///
        /// This trait provides a way to borrow a handle as a
        #[doc = concat!(stringify!($borrowed), ".")]
        pub trait $as_borrowed {
            #[doc = concat!("Borrows the handle as a ", stringify!($borrowed), ".")]
            fn as_borrowed(&self) -> $borrowed<'_>;
        }

        impl $owned {
            #[doc = concat!("Creates a new ", stringify!($owned), " from a raw handle.")]
            ///
            /// # Safety
            ///
            /// The caller must ensure that the handle is valid and that they
            /// have ownership of it. The handle will be closed when this
            /// object is dropped.
            #[inline]
            pub unsafe fn from_raw(handle: $raw) -> Self {
                Self { handle }
            }

            #[doc = concat!("Consumes the ", stringify!($owned), " and returns the raw handle.")]
            ///
            /// This method consumes the owned handle and returns the raw handle
            /// without closing it. The caller becomes responsible for properly
            /// closing the handle.
            #[inline]
            pub fn into_raw(self) -> $raw {
                let handle = self.handle;
                std::mem::forget(self);
                handle
            }
        }

        impl<'a> $borrowed<'a> {
            #[doc = concat!("Creates a new ", stringify!($borrowed), " from a raw handle.")]
            ///
            /// # Safety
            ///
            /// The caller must ensure that the handle is valid and will remain
            /// valid for the lifetime `'a`.
            #[inline]
            pub unsafe fn borrow_raw(handle: $raw) -> Self {
                Self {
                    handle,
                    _phantom: std::marker::PhantomData,
                }
            }
        }

        impl $as_raw for $owned {
            #[inline]
            fn as_raw(&self) -> $raw {
                self.handle
            }
        }

        impl $as_raw for $borrowed<'_> {
            #[inline]
            fn as_raw(&self) -> $raw {
                self.handle
            }
        }

        impl $as_borrowed for $owned {
            #[inline]
            fn as_borrowed(&self) -> $borrowed<'_> {
                unsafe { $borrowed::borrow_raw(self.handle) }
            }
        }

        impl Drop for $owned {
            #[inline]
            fn drop(&mut self) {
                // Call the provided drop function
                ($drop_fn)(self.handle);
            }
        }

        impl PartialEq for $owned {
            #[inline]
            fn eq(&self, other: &Self) -> bool {
                self.handle == other.handle
            }
        }

        impl Eq for $owned {}

        impl PartialOrd for $owned {
            #[inline]
            fn partial_cmp(&self, other: &Self) -> Option<std::cmp::Ordering> {
                Some(self.cmp(other))
            }
        }

        impl Ord for $owned {
            #[inline]
            fn cmp(&self, other: &Self) -> std::cmp::Ordering {
                self.handle.cmp(&other.handle)
            }
        }

        impl std::hash::Hash for $owned {
            #[inline]
            fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
                self.handle.hash(state);
            }
        }

        // Additional trait implementations for better ergonomics
        impl From<$owned> for $raw {
            #[inline]
            fn from(owned: $owned) -> Self {
                owned.into_raw()
            }
        }

        impl<'a> From<&'a $owned> for $borrowed<'a> {
            #[inline]
            fn from(owned: &'a $owned) -> Self {
                owned.as_borrowed()
            }
        }

        impl<'a> From<$borrowed<'a>> for $raw {
            #[inline]
            fn from(borrowed: $borrowed<'a>) -> Self {
                borrowed.as_raw()
            }
        }
    };
}

define_handle! {
    found::HWND,
    RawHwnd,
    AsRawHwnd,
    OwnedHwnd,
    BorrowedHwnd,
    AsBorrowedHwnd,
    |handle| unsafe {
        winmsg::DestroyWindow(handle)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::ptr;

    #[test]
    fn test_handle_basic_functionality() {
        // Test with a non-null handle value
        let raw_handle = 0x12345678 as RawHwnd;

        // Test creating an owned handle
        let owned = unsafe { OwnedHwnd::from_raw(raw_handle) };

        // Test AsRawHwnd trait
        assert_eq!(owned.as_raw(), raw_handle);

        // Test borrowing
        let borrowed = owned.as_borrowed();
        assert_eq!(borrowed.as_raw(), raw_handle);

        // Test conversion traits
        let borrowed_from_ref: BorrowedHwnd = (&owned).into();
        assert_eq!(borrowed_from_ref.as_raw(), raw_handle);

        // Test into_raw (consumes the owned handle)
        let raw_back = owned.into_raw();
        assert_eq!(raw_back, raw_handle);
    }

    #[test]
    fn test_borrowed_handle() {
        let raw_handle = 0xABCDEF00 as RawHwnd;

        // Test creating a borrowed handle directly
        let borrowed = unsafe { BorrowedHwnd::borrow_raw(raw_handle) };
        assert_eq!(borrowed.as_raw(), raw_handle);

        // Test that borrowed handles can be copied
        let borrowed_copy = borrowed;
        assert_eq!(borrowed_copy.as_raw(), raw_handle);
    }

    #[test]
    fn test_handle_equality_and_ordering() {
        let handle1 = unsafe { OwnedHwnd::from_raw(0x1000 as RawHwnd) };
        let handle2 = unsafe { OwnedHwnd::from_raw(0x1000 as RawHwnd) };
        let handle3 = unsafe { OwnedHwnd::from_raw(0x2000 as RawHwnd) };

        // Test equality
        assert_eq!(handle1, handle2);
        assert_ne!(handle1, handle3);

        // Test ordering
        assert!(handle1 < handle3);
        assert!(handle3 > handle1);

        // Consume handles to avoid drop issues in test
        let _ = handle1.into_raw();
        let _ = handle2.into_raw();
        let _ = handle3.into_raw();
    }

    #[test]
    fn test_null_handle() {
        let null_handle = unsafe { OwnedHwnd::from_raw(ptr::null_mut()) };
        assert_eq!(null_handle.as_raw(), ptr::null_mut());

        let borrowed = null_handle.as_borrowed();
        assert_eq!(borrowed.as_raw(), ptr::null_mut());

        // Consume to avoid drop
        let _ = null_handle.into_raw();
    }
}
