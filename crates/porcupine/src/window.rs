// MIT/Apache2 License

//! Window functionality.

use crate::Result;
use crate::handle::AsRawHwnd;
use crate::handle::BorrowedHwnd;
use crate::handle::OwnedHwnd;
use crate::wstr::WStr;

use windows_sys::Win32::Foundation as found;
use windows_sys::Win32::UI::WindowsAndMessaging as winmsg;

use std::marker::PhantomData;
use std::mem;
use std::num::NonZeroI32;
use std::ptr;

/// Builder for a window class.
pub struct WindowClassBuilder<'a, WinData> {
    /// Underlying builder.
    builder: winmsg::WNDCLASSEXW,

    /// Eat type parameters.
    _eat: PhantomData<(&'a (), WinData)>,
}

impl<'a> WindowClassBuilder<'a, ()> {
    /// Creates a new window class builder.
    pub fn new(name: &'a WStr) -> Self {
        Self {
            builder: winmsg::WNDCLASSEXW {
                cbSize: mem::size_of::<winmsg::WNDCLASSEXW>() as u32,
                style: 0,
                lpfnWndProc: None,
                cbClsExtra: 0,
                cbWndExtra: 0,
                hInstance: ptr::null_mut(),
                hIcon: ptr::null_mut(),
                hCursor: ptr::null_mut(),
                hbrBackground: ptr::null_mut(),
                lpszMenuName: ptr::null(),
                lpszClassName: name.as_ptr(),
                hIconSm: ptr::null_mut(),
            },
            _eat: PhantomData,
        }
    }
}

impl<'a> WindowClassBuilder<'a, ()> {
    /// Set window-specific data.
    pub fn with_window_data<WinData>(mut self) -> WindowClassBuilder<'a, WinData> {
        self.builder.cbWndExtra = mem::size_of::<WinData>().try_into().unwrap();
        WindowClassBuilder {
            builder: self.builder,
            _eat: PhantomData,
        }
    }
}

impl<'a, WinData> WindowClassBuilder<'a, WinData> {
    /// Build the window class.
    pub fn build(mut self) -> Result<WindowClass<WinData>> {
        self.builder.lpfnWndProc = Some(initial_window_proc::<WinData>);

        let atom = unsafe { winmsg::RegisterClassExW(&self.builder) };

        if atom == 0 {
            return Err(crate::Win32Error::from_last_error("RegisterClassExW"));
        }

        Ok(WindowClass {
            atom,
            _eat: PhantomData,
        })
    }
}

/// Window class.
pub struct WindowClass<WinData> {
    /// Atom.
    atom: u16,
    _eat: PhantomData<WinData>,
}

impl<WinData> Clone for WindowClass<WinData> {
    fn clone(&self) -> Self {
        *self
    }
}

impl<WinData> Copy for WindowClass<WinData> {}

/// Window message.
pub struct WindowMessage<'a> {
    /// Borrowed window handle.
    hwnd: BorrowedHwnd<'a>,

    /// Message code.
    code: u32,

    /// WPARAM.
    wparam: found::WPARAM,

    /// LPARAM.
    lparam: found::LPARAM,
}

/// Result of a window call.
pub enum WindowResult {
    /// Just forward to the next procedure.
    DefWindowProc,

    /// Return a value.
    Return(isize),
}

/// A wrapper around a window that tracks lifetimes of captured variables.
pub struct Window<'a> {
    /// Underlying window handle.
    hwnd: OwnedHwnd,

    /// Phantom data.
    _eat: PhantomData<dyn Fn(&'a ()) + 'a>,
}

/// Create a new window.
#[allow(clippy::too_many_arguments)]
pub fn create_window<'a, WinData: 'a, F>(
    style: u32, // TODO: Typed
    class: WindowClass<WinData>,
    name: Option<&WStr>,
    x: Option<NonZeroI32>,
    y: Option<NonZeroI32>,
    width: u32,
    height: u32,
    parent: Option<BorrowedHwnd<'_>>,
    // TODO: Menu
    callback: F,
    window_data: WinData,
) -> Result<Window<'a>>
where
    F: Fn(&mut WinData, WindowMessage<'_>) -> WindowResult + 'a,
{
    let mut create_data = Some(WindowCreation {
        callback: window_proc::<WinData, F>,
        user_data: store_callback(callback),
        window_data,
    });

    // Create the window.
    let hwnd = unsafe {
        winmsg::CreateWindowExW(
            0,
            class.atom as *const u16,
            name.map_or(ptr::null(), |n| n.as_ptr()),
            style,
            x.map_or(winmsg::CW_USEDEFAULT, |x| x.get()),
            y.map_or(winmsg::CW_USEDEFAULT, |y| y.get()),
            width.try_into().unwrap(),
            height.try_into().unwrap(),
            parent.map_or(ptr::null_mut(), |p| p.as_raw()),
            ptr::null_mut(),
            ptr::null_mut(),
            &mut create_data as *mut Option<WindowCreation<WinData>> as *mut std::ffi::c_void,
        )
    };

    if hwnd.is_null() {
        return Err(crate::Win32Error::from_last_error("CreateWindowExW"));
    }

    Ok(Window {
        hwnd: unsafe { OwnedHwnd::from_raw(hwnd) },
        _eat: PhantomData,
    })
}

/// Window creation data.
struct WindowCreation<WinData> {
    /// Callback to use.
    callback:
        unsafe extern "system" fn(found::HWND, u32, found::WPARAM, found::LPARAM) -> found::LRESULT,

    /// First part of the user data.
    user_data: *mut (),

    /// Window-specific data.
    window_data: WinData,
}

/// Starting window procedure for a class/window.
unsafe extern "system" fn initial_window_proc<WinData>(
    hwnd: found::HWND,
    msg: u32,
    wparam: found::WPARAM,
    lparam: found::LPARAM,
) -> found::LRESULT {
    crate::abort_on_panic(move || {
        // Can't do anything with a null window handle.
        if hwnd.is_null() {
            return unsafe { winmsg::DefWindowProcW(hwnd, msg, wparam, lparam) };
        }

        // We only do anything special for WM_NCCREATE.
        if msg != winmsg::WM_NCCREATE {
            return unsafe { winmsg::DefWindowProcW(hwnd, msg, wparam, lparam) };
        }

        // Grab the CREATESTRUCT.
        let create_struct = unsafe { &mut *(lparam as *mut winmsg::CREATESTRUCTW) };

        // Read out the creation data.
        let creation_data = {
            let data = unsafe {
                &mut *(create_struct.lpCreateParams as *mut Option<WindowCreation<WinData>>)
            };
            match data.take() {
                Some(data) => data,
                None => return 0,
            }
        };

        // Add the data to the window.
        let window_data = unsafe {
            // Set wndproc and user data.
            winmsg::SetWindowLongPtrW(
                hwnd,
                winmsg::GWLP_WNDPROC,
                creation_data.callback as usize as isize,
            );
            winmsg::SetWindowLongPtrW(
                hwnd,
                winmsg::GWLP_USERDATA,
                creation_data.user_data as isize,
            );

            // Grab pointer to extra window data.
            let extra_data = winmsg::GetWindowLongPtrW(hwnd, 0) as *mut WinData;
            &mut *extra_data
        };
        *window_data = creation_data.window_data;

        // Everything should be set now.
        unsafe { winmsg::DefWindowProcW(hwnd, msg, wparam, lparam) }
    })
}

/// Main procedure for a window.
unsafe extern "system" fn window_proc<WinData, F>(
    hwnd: found::HWND,
    msg: u32,
    wparam: found::WPARAM,
    lparam: found::LPARAM,
) -> found::LRESULT
where
    F: Fn(&mut WinData, WindowMessage<'_>) -> WindowResult,
{
    crate::abort_on_panic(move || {
        // Can't do anything with a null window handle.
        if hwnd.is_null() {
            return unsafe { winmsg::DefWindowProcW(hwnd, msg, wparam, lparam) };
        }

        // Get the window data.
        let window_data = unsafe { &mut *(winmsg::GetWindowLongPtrW(hwnd, 0) as *mut WinData) };

        // Get the callback information.
        let callback_data =
            unsafe { winmsg::GetWindowLongPtrW(hwnd, winmsg::GWLP_USERDATA) as *const () };

        // We are borrowing it.
        let callback = unsafe { borrow_callback::<F>(&callback_data) };

        // Call the callback.
        // TODO: Handle panics more elegantly
        let result = (callback)(
            window_data,
            WindowMessage {
                hwnd: unsafe { BorrowedHwnd::borrow_raw(hwnd) },
                code: msg,
                wparam,
                lparam,
            },
        );

        // Drop the callback if this is WM_NCDESTROY.
        if msg == winmsg::WM_NCDESTROY {
            drop_callback::<F>(callback_data as *mut ());
        }

        // Operate based on the result.
        match result {
            WindowResult::DefWindowProc => unsafe {
                winmsg::DefWindowProcW(hwnd, msg, wparam, lparam)
            },
            WindowResult::Return(value) => value,
        }
    })
}

/// Store a callback into a pointer slot.
fn store_callback<F>(callback: F) -> *mut () {
    if mem::size_of::<F>() == 0 {
        // Just do null.
        return ptr::null_mut();
    } else if mem::size_of::<F>() <= mem::size_of::<usize>() {
        // Store in an array.
        let mut data = [0u8; mem::size_of::<usize>()];
        unsafe {
            ptr::write(data.as_mut_ptr().cast(), callback);
        }
        return usize::from_ne_bytes(data) as *mut ();
    }

    // Allocate it on the heap.
    Box::into_raw(Box::new(callback)).cast()
}

/// Borrow a callback from a pointer slot.
unsafe fn borrow_callback<F>(ptr: &*const ()) -> &F {
    if mem::size_of::<F>() == 0 {
        // Can be any value.
        const DUMMY: [usize; 0] = [0; 0];
        unsafe { &*(ptr::from_ref(&DUMMY) as *const F) }
    } else if mem::size_of::<F>() <= mem::size_of::<usize>() {
        // The data is contained inside of the pointer.
        unsafe { &*ptr::from_ref(ptr).cast::<F>() }
    } else {
        // This is a heap pointer.
        let value = *ptr;
        unsafe { &*(value.cast::<F>()) }
    }
}

/// Drop a callback from a pointer slot.
fn drop_callback<F>(ptr: *mut ()) {
    if mem::size_of::<F>() == 0 {
        // Drop a random value.
        drop(unsafe { mem::zeroed::<F>() });
    } else if mem::size_of::<F>() <= mem::size_of::<usize>() {
        // Cast back to a value and drop it.
        let mut value = mem::MaybeUninit::<F>::uninit();
        let bytes = usize::to_ne_bytes(ptr as usize);
        unsafe {
            ptr::copy_nonoverlapping(
                bytes.as_ptr(),
                value.as_mut_ptr().cast(),
                mem::size_of::<F>(),
            );
        }
        drop(unsafe { value.assume_init() });
    } else {
        // This is a heap pointer.
        drop(unsafe { Box::from_raw(ptr.cast::<F>()) });
    }
}
