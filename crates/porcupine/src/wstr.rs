// MIT/Apache2 License

use std::fmt::{self, Write};

/// Borrowed UTF-16 C-string.
#[repr(transparent)]
pub struct WStr([u16]);

/// Owned UTF-16 C-string.
pub struct WString(Vec<u16>);

impl WStr {
    /// Creates a `WStr` from a slice of u16 values.
    ///
    /// # Safety
    ///
    /// The caller must ensure that the slice represents a valid null-terminated
    /// UTF-16 string.
    pub unsafe fn from_slice_unchecked(slice: &[u16]) -> &Self {
        unsafe { std::mem::transmute(slice) }
    }

    /// Creates a `WStr` from a raw pointer.
    ///
    /// # Safety
    ///
    /// The caller must ensure that:
    /// - The pointer is valid and points to a null-terminated UTF-16 string
    /// - The string remains valid for the lifetime of the returned reference
    pub unsafe fn from_ptr<'a>(ptr: *const u16) -> &'a Self {
        unsafe {
            if ptr.is_null() {
                return Self::from_slice_unchecked(&[]);
            }

            let mut len = 0;
            while *ptr.add(len) != 0 {
                len += 1;
            }

            let slice = std::slice::from_raw_parts(ptr, len + 1);
            Self::from_slice_unchecked(slice)
        }
    }

    /// Returns the underlying slice of u16 values.
    pub fn as_slice(&self) -> &[u16] {
        &self.0
    }

    /// Return a pointer to the underlying data.
    pub fn as_ptr(&self) -> *const u16 {
        self.0.as_ptr()
    }

    /// Converts the wide string to a Rust String, replacing invalid UTF-16 sequences
    /// with the replacement character.
    pub fn to_string_lossy(&self) -> String {
        // Remove the null terminator if present
        let slice = if self.0.last() == Some(&0) {
            &self.0[..self.0.len() - 1]
        } else {
            &self.0
        };

        String::from_utf16_lossy(slice)
    }
}

impl WString {
    /// Creates a new empty `WString`.
    pub fn new() -> Self {
        Self(Vec::new())
    }

    /// Creates a `WString` from a Rust string.
    pub fn from_string(s: &str) -> Self {
        let mut wide: Vec<u16> = s.encode_utf16().collect();
        wide.push(0); // null terminator
        Self(wide)
    }

    /// Creates a `WString` with the specified capacity.
    pub fn with_capacity(capacity: usize) -> Self {
        Self(Vec::with_capacity(capacity))
    }

    /// Returns the underlying Vec<u16>.
    pub fn into_vec(self) -> Vec<u16> {
        self.0
    }

    /// Returns a reference to the underlying data.
    pub fn as_slice(&self) -> &[u16] {
        &self.0
    }

    /// Returns a mutable reference to the underlying Vec<u16>.
    ///
    /// # Safety
    ///
    /// The caller must ensure that the vector represents a valid null-terminated
    /// UTF-16 string.
    pub unsafe fn as_mut_vec(&mut self) -> &mut Vec<u16> {
        &mut self.0
    }

    /// Converts to a `WStr`.
    pub fn as_wstr(&self) -> &WStr {
        unsafe { WStr::from_slice_unchecked(&self.0) }
    }
}

impl Default for WString {
    fn default() -> Self {
        Self::new()
    }
}

impl fmt::Display for WStr {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        for chr in std::char::decode_utf16(self.0.iter().cloned()) {
            f.write_char(chr.unwrap_or(std::char::REPLACEMENT_CHARACTER))?;
        }
        Ok(())
    }
}

impl fmt::Debug for WStr {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "WStr({self})")
    }
}

impl fmt::Display for WString {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.as_wstr())
    }
}

impl fmt::Debug for WString {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "WString({})", self.as_wstr())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_wstring_from_string() {
        let wstring = WString::from_string("Hello, World!");
        let converted = wstring.as_wstr().to_string_lossy();
        assert_eq!(converted, "Hello, World!");
    }

    #[test]
    fn test_wstring_empty() {
        let wstring = WString::new();
        let converted = wstring.as_wstr().to_string_lossy();
        assert_eq!(converted, "");
    }

    #[test]
    fn test_wstring_with_unicode() {
        let wstring = WString::from_string("Hello, 世界! 🌍");
        let converted = wstring.as_wstr().to_string_lossy();
        assert_eq!(converted, "Hello, 世界! 🌍");
    }

    #[test]
    fn test_wstr_from_slice() {
        let data = [72u16, 101, 108, 108, 111, 0]; // "Hello\0"
        let wstr = unsafe { WStr::from_slice_unchecked(&data) };
        let converted = wstr.to_string_lossy();
        assert_eq!(converted, "Hello");
    }

    #[test]
    fn test_wstring_display() {
        let wstring = WString::from_string("Test Display");
        let display_str = format!("{}", wstring);
        assert_eq!(display_str, "Test Display");
    }

    #[test]
    fn test_wstring_debug() {
        let wstring = WString::from_string("Test Debug");
        let debug_str = format!("{:?}", wstring);
        assert!(debug_str.contains("WString"));
        assert!(debug_str.contains("Test Debug"));
    }

    #[test]
    fn test_wstr_display() {
        let data = [84u16, 101, 115, 116, 0]; // "Test\0"
        let wstr = unsafe { WStr::from_slice_unchecked(&data) };
        let display_str = format!("{}", wstr);
        assert_eq!(display_str, "Test");
    }

    #[test]
    fn test_wstr_debug() {
        let data = [84u16, 101, 115, 116, 0]; // "Test\0"
        let wstr = unsafe { WStr::from_slice_unchecked(&data) };
        let debug_str = format!("{:?}", wstr);
        assert!(debug_str.contains("WStr"));
        assert!(debug_str.contains("Test"));
    }
}
