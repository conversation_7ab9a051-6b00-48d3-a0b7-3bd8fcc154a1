// MIT/Apache2 License

#![cfg(windows)]

use windows_sys::Win32::Foundation as found;
use windows_sys::Win32::System::Diagnostics::Debug as debug;

use std::fmt;

pub mod handle;
pub mod window;
pub mod wstr;

/// Win32 error.
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct Win32Error {
    /// Error code.
    code: u32,

    /// Function name.
    function: &'static str,
}

impl Win32Error {
    /// Creates a new Win32Error with the given error code and function name.
    pub fn new(code: u32, function: &'static str) -> Self {
        Self { code, function }
    }

    /// Creates a Win32Error from the last error code returned by GetLastError.
    pub fn from_last_error(function: &'static str) -> Self {
        let code = unsafe { found::GetLastError() };
        Self::new(code, function)
    }

    /// Returns the error code.
    pub fn code(&self) -> u32 {
        self.code
    }

    /// Returns the function name where the error occurred.
    pub fn function(&self) -> &'static str {
        self.function
    }

    /// Formats the error message using FormatMessageW.
    fn format_message<R>(&self, msg_handler: impl FnOnce(&wstr::WStr) -> R) -> R {
        const BUFFER_SIZE: usize = 1024;
        let mut buffer = [0u16; BUFFER_SIZE];

        let length = unsafe {
            debug::FormatMessageW(
                debug::FORMAT_MESSAGE_FROM_SYSTEM | debug::FORMAT_MESSAGE_IGNORE_INSERTS,
                std::ptr::null(),
                self.code,
                0, // Default language
                buffer.as_mut_ptr(),
                BUFFER_SIZE as u32,
                std::ptr::null_mut(),
            )
        };

        if length == 0 {
            // If FormatMessageW fails, return a generic message
            let msg = wstr::WString::from_string(&format!("Unknown error: {}", self.code));
            msg_handler(msg.as_wstr())
        } else {
            // Convert the wide string to a Rust string
            let wstr = unsafe { wstr::WStr::from_slice_unchecked(&buffer[..length as usize]) };
            msg_handler(wstr)
        }
    }
}

impl fmt::Display for Win32Error {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        self.format_message(|msg| write!(f, "Error in {}: {}", self.function, msg))
    }
}

impl std::error::Error for Win32Error {
    fn description(&self) -> &str {
        "Win32 API error"
    }
}

pub type Result<T = ()> = std::result::Result<T, Win32Error>;

/// Abort a function if it panics.
fn abort_on_panic<R, F: FnOnce() -> R>(f: F) -> R {
    struct Bomb;
    impl Drop for Bomb {
        fn drop(&mut self) {
            std::process::abort();
        }
    }

    let bomb = Bomb;
    let res = f();
    std::mem::forget(bomb);
    res
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_win32_error_creation() {
        let error = Win32Error::new(2, "CreateFile");
        assert_eq!(error.code(), 2);
        assert_eq!(error.function(), "CreateFile");
    }

    #[test]
    fn test_win32_error_display() {
        let error = Win32Error::new(2, "CreateFile");
        let display_str = format!("{}", error);

        // Should contain the function name
        assert!(display_str.contains("CreateFile"));

        // Should contain some error message (exact message depends on system)
        assert!(!display_str.is_empty());
    }

    #[test]
    fn test_win32_error_debug() {
        let error = Win32Error::new(2, "CreateFile");
        let debug_str = format!("{:?}", error);

        // Debug should show the struct fields
        assert!(debug_str.contains("Win32Error"));
        assert!(debug_str.contains("2"));
        assert!(debug_str.contains("CreateFile"));
    }

    #[test]
    fn test_win32_error_from_last_error() {
        // This test just ensures the function doesn't panic
        // The actual error code will depend on the system state
        let error = Win32Error::from_last_error("TestFunction");
        assert_eq!(error.function(), "TestFunction");

        // Error code should be some value (could be 0 for success)
        let _code = error.code();
    }

    #[test]
    fn test_win32_error_as_std_error() {
        let error = Win32Error::new(5, "OpenProcess");
        let std_error: &dyn std::error::Error = &error;

        // Should be able to display
        let display = format!("{std_error}");
        assert!(display.contains("OpenProcess"));
    }
}
