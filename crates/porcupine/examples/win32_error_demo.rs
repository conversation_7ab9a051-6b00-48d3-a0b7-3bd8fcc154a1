// MIT/Apache2 License

//! Demonstration of Win32Error functionality.

#[cfg(windows)]
fn main() {
    use porcupine::Win32Error;

    println!("Win32Error Demo");
    println!("===============");

    // Example 1: Create an error with a known error code
    println!("\n1. Creating Win32Error with known error code (2 - File not found):");
    let error = Win32Error::new(2, "CreateFile");
    println!("   Error: {}", error);
    println!("   Debug: {:?}", error);
    println!("   Code: {}", error.code());
    println!("   Function: {}", error.function());

    // Example 2: Create an error from GetLastError
    println!("\n2. Creating Win32Error from GetLastError:");
    let last_error = Win32Error::from_last_error("SomeFunction");
    println!("   Error: {}", last_error);
    println!("   Code: {}", last_error.code());

    // Example 3: Using as std::error::Error
    println!("\n3. Using Win32Error as std::error::Error:");
    let error = Win32Error::new(5, "OpenProcess");
    let std_error: &dyn std::error::Error = &error;
    println!("   As std::error::Error: {}", std_error);

    // Example 4: Common Windows error codes
    println!("\n4. Common Windows error codes:");
    let common_errors = [
        (0, "ERROR_SUCCESS"),
        (1, "ERROR_INVALID_FUNCTION"),
        (2, "ERROR_FILE_NOT_FOUND"),
        (3, "ERROR_PATH_NOT_FOUND"),
        (5, "ERROR_ACCESS_DENIED"),
        (6, "ERROR_INVALID_HANDLE"),
        (87, "ERROR_INVALID_PARAMETER"),
    ];

    for (code, name) in common_errors {
        let error = Win32Error::new(code, name);
        println!("   {}: {}", name, error);
    }
}

#[cfg(not(windows))]
fn main() {
    println!("This example only works on Windows.");
    println!(
        "Use 'cargo run --example win32_error_demo --target x86_64-pc-windows-gnu' with wine to test on Linux."
    );
}
